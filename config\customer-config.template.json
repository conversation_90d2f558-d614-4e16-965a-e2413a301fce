{"customer": {"name": "Bệnh viện ABC", "code": "BV_ABC", "domain": "bv-abc.com", "contact": {"email": "<EMAIL>", "phone": "+84 123 456 789", "address": "123 Đường ABC, Quận XYZ, TP. HCM"}}, "branding": {"appName": "Quản lý TBYT - BV ABC", "shortName": "TBYT ABC", "description": "<PERSON><PERSON> thống quản lý thiết bị y tế Bệnh viện ABC", "primaryColor": "#2563eb", "secondaryColor": "#f59e0b", "logo": "/assets/bv-abc-logo.png", "favicon": "/assets/bv-abc-favicon.ico"}, "supabase": {"url": "https://your-project.supabase.co", "anonKey": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "serviceKey": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "github": {"repoName": "bv-abc-medical-equipment", "orgName": "your-organization", "visibility": "private"}, "deployment": {"platform": "vercel", "customDomain": "tbyt.bv-abc.com", "environment": "production"}, "features": {"enableQRScanner": true, "enableReports": true, "enableTransfers": true, "enableMaintenance": true, "enableUserManagement": true, "enableRealtime": true}, "sampleData": {"departments": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Phòng <PERSON>", "<PERSON><PERSON>ng Chẩn đo<PERSON> hình <PERSON>nh"], "equipmentTypes": ["<PERSON><PERSON><PERSON><PERSON> bị y tế", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> đ<PERSON>", "<PERSON><PERSON><PERSON><PERSON> bị phẫu thuật"], "adminUser": {"username": "admin", "password": "admin123", "fullName": "<PERSON><PERSON><PERSON>n trị viên BV ABC", "role": "admin"}}, "customization": {"roles": {"admin": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON> hệ thống", "to_qltb": "Tổ QLTB BV ABC", "qltb_khoa": "QLTB Khoa/Phòng", "user": "Nhân viên"}, "equipmentStatuses": ["<PERSON><PERSON> sử dụng", "Chờ sửa chữa", "Chờ bảo trì", "Ngừng sử dụng", "<PERSON><PERSON>ý"], "maintenanceTypes": ["<PERSON><PERSON><PERSON> trì đ<PERSON><PERSON> kỳ", "<PERSON><PERSON><PERSON> trì đột xuất", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"]}}